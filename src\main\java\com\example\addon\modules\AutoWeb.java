package com.example.addon.modules;

import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.block.BlockState;

import java.util.HashSet;
import java.util.Set;

public class AutoWeb extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgAiming = settings.createGroup("Aiming");
    private final SettingGroup sgCombat = settings.createGroup("Combat");

    // General settings
    private final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder()
        .name("range")
        .description("Maximum range to place webs.")
        .defaultValue(4.0)
        .min(0.0)
        .max(6.0)
        .sliderMax(6.0)
        .build()
    );

    private final Setting<Boolean> selfProtect = sgGeneral.add(new BoolSetting.Builder()
        .name("self-protect")
        .description("Avoid placing webs on yourself.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> predictMovement = sgGeneral.add(new BoolSetting.Builder()
        .name("predict-movement")
        .description("Predict player movement and place webs ahead.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("delay")
        .description("Delay between web placements in ticks.")
        .defaultValue(3)
        .min(0)
        .max(20)
        .sliderMax(20)
        .build()
    );

    private final Setting<Boolean> onlyWhileFalling = sgGeneral.add(new BoolSetting.Builder()
        .name("only-while-falling")
        .description("Only place webs when the target player is airborne (falling or jumping).")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> targetInAirOnly = sgGeneral.add(new BoolSetting.Builder()
        .name("target-in-air-only")
        .description("Only place webs when the target is in the air (not on ground).")
        .defaultValue(true)
        .build()
    );

    // Combat settings
    private final Setting<Boolean> inCombatCheck = sgCombat.add(new BoolSetting.Builder()
        .name("in-combat-check")
        .description("Only activate when in combat.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> combatRange = sgCombat.add(new DoubleSetting.Builder()
        .name("combat-range")
        .description("Range to consider being in combat.")
        .defaultValue(6.0)
        .min(0.0)
        .max(10.0)
        .sliderMax(10.0)
        .visible(() -> inCombatCheck.get())
        .build()
    );

    private final Setting<Integer> combatTimeout = sgCombat.add(new IntSetting.Builder()
        .name("combat-timeout")
        .description("Ticks to remain in combat after last enemy contact.")
        .defaultValue(100)
        .min(0)
        .max(400)
        .sliderMax(400)
        .visible(() -> inCombatCheck.get())
        .build()
    );

    // Aiming settings
    private final Setting<Boolean> smoothAiming = sgAiming.add(new BoolSetting.Builder()
        .name("smooth-aiming")
        .description("Use smooth aiming when placing webs.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Double> aimSpeed = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("Speed of smooth aiming.")
        .defaultValue(0.3)
        .min(0.1)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Boolean> exponentialSmoothing = sgAiming.add(new BoolSetting.Builder()
        .name("exponential-smoothing")
        .description("Use exponential smoothing for even smoother aiming.")
        .defaultValue(false)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private final Setting<Double> smoothingFactor = sgAiming.add(new DoubleSetting.Builder()
        .name("smoothing-factor")
        .description("Exponential smoothing factor (higher = smoother).")
        .defaultValue(0.7)
        .min(0.0)
        .max(1.0)
        .sliderMax(1.0)
        .visible(() -> smoothAiming.get() && exponentialSmoothing.get())
        .build()
    );

    private final Setting<Boolean> aimAtCenter = sgAiming.add(new BoolSetting.Builder()
        .name("aim-at-center")
        .description("Aim at the center of the block when placing.")
        .defaultValue(true)
        .visible(() -> smoothAiming.get())
        .build()
    );

    private int timer = 0;
    private long lastFrameTime = System.nanoTime();
    private BlockPos targetBlock = null;
    private boolean isAiming = false;
    private int lastCombatTime = 0;
    private PlayerEntity target = null;
    
    // Track which players already have webs placed for them (no time expiration)
    private final Set<PlayerEntity> webbedPlayers = new HashSet<>();

    public AutoWeb() {
        super(Categories.Combat, "auto-web", "Automatically places webs on nearby players with smart targeting.");
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        // Check if another module has already taken action this tick
        if (hasAnotherModuleTakenAction()) {
            return;
        }

        // Update combat status
        updateCombatStatus();
        
        // Clean up webbed players set (remove players that are no longer webbed)
        cleanupWebbedPlayers();

        // Check combat requirement
        if (inCombatCheck.get() && !isInCombat()) {
            isAiming = false;
            targetBlock = null;
            return;
        }

        // Calculate delta time for smooth aiming
        long currentTime = System.nanoTime();
        float deltaTime = (currentTime - lastFrameTime) / 1_000_000_000.0f;
        lastFrameTime = currentTime;

        // Check if we have webs in inventory
        FindItemResult webs = InvUtils.find(Items.COBWEB);
        if (!webs.found()) {
            isAiming = false;
            targetBlock = null;
            return;
        }

        // Decrement timer
        if (timer > 0) {
            timer--;
        }

        // If we're currently aiming at a block, continue the smooth aiming process
        if (isAiming && targetBlock != null) {
            // Try to place when timer is ready
            if (timer == 0 && canPlaceAt(targetBlock)) {
                if (webs.getHand() == null && !InvUtils.swap(webs.slot(), false)) {
                    isAiming = false;
                    targetBlock = null;
                    return;
                }

                if (BlockUtils.place(targetBlock, webs, false, 0)) {
                    markActionTaken(); // Notify action coordinator
                    timer = delay.get();
                    isAiming = false;
                    targetBlock = null;
                }
            }
            return;
        }

        // Find target player if we're not currently aiming
            if (timer == 0) {
                target = findTarget();
                if (target != null) {
                    BlockPos placePos = getPlacementPos(target);
                    if (placePos != null && canPlaceAt(placePos) && hasReachableAdjacentBlock(placePos)) {
                        // Only place web if target is about to land or stop moving
                        Vec3d velocity = target.getVelocity();
                        if (velocity.lengthSquared() < 0.01 || // Nearly stopped
                            (velocity.y < 0 && target.getPos().y - placePos.getY() < 0.5)) { // About to land

                            targetBlock = placePos;
                            isAiming = smoothAiming.get();

                            // Mark this player as webbed (no expiration)
                            webbedPlayers.add(target);

                            if (!smoothAiming.get()) {
                                // Instant placement without smooth aiming
                                if (webs.getHand() == null && !InvUtils.swap(webs.slot(), false)) return;

                                if (BlockUtils.place(placePos, webs, false, 0)) {
                                    markActionTaken(); // Notify action coordinator
                                    timer = delay.get();
                                }
                                targetBlock = null;
                            }
                        }
                    }
                }
            }
    }

    private void updateCombatStatus() {
        if (!inCombatCheck.get()) return;

        boolean foundEnemy = false;
        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player) continue;
            if (player.isDead() || player.getHealth() <= 0) continue;
            
            double distance = mc.player.distanceTo(player);
            if (distance <= combatRange.get()) {
                foundEnemy = true;
                break;
            }
        }

        if (foundEnemy) {
            lastCombatTime = combatTimeout.get();
        } else if (lastCombatTime > 0) {
            lastCombatTime--;
        }
    }

    private boolean isInCombat() {
        return lastCombatTime > 0;
    }

    private void cleanupWebbedPlayers() {
        webbedPlayers.removeIf(player -> {
            // Remove if player is dead, too far away, or no longer in web
            if (player.isDead() || mc.player.distanceTo(player) > range.get() * 2) {
                return true;
            }
            
            // Check if player is still in web
            return !isPlayerInWeb(player);
        });
    }

    private PlayerEntity findTarget() {
        PlayerEntity target = null;
        double closestDistance = Double.MAX_VALUE;

        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player) continue;
            if (player.isDead() || player.getHealth() <= 0) continue;
            if (selfProtect.get() && player == mc.player) continue;

            double distance = mc.player.distanceTo(player);
            if (distance > range.get()) continue;

            // Check if target is airborne (falling/jumping) when setting is enabled
            if (targetInAirOnly.get() && !isPlayerAirborne(player)) continue;

            // Skip if this player already has a web placed for them
            if (webbedPlayers.contains(player)) continue;

            // Check if player is already in a web
            if (isPlayerInWeb(player)) continue;

            if (distance < closestDistance) {
                closestDistance = distance;
                target = player;
            }
        }

        return target;
    }

    private boolean isPlayerAirborne(PlayerEntity player) {
        // Consider player airborne if they are not on the ground
        return !player.isOnGround();
    }

    private boolean isPlayerInWeb(PlayerEntity player) {
        BlockPos playerPos = new BlockPos((int) Math.floor(player.getX()), 
                                        (int) Math.floor(player.getY()), 
                                        (int) Math.floor(player.getZ()));
        
        // Check current position and position above (head level)
        return mc.world.getBlockState(playerPos).getBlock() == Blocks.COBWEB ||
               mc.world.getBlockState(playerPos.up()).getBlock() == Blocks.COBWEB;
    }

    @EventHandler
    private void onRender3d(Render3DEvent event) {
        if (mc.player == null || target == null || targetBlock == null || !smoothAiming.get()) {
            return;
        }

        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetAimPos;

        if (aimAtCenter.get()) {
            targetAimPos = new Vec3d(targetBlock.getX() + 0.5, targetBlock.getY() + 0.5, targetBlock.getZ() + 0.5);
        } else {
            // Calculate the point on the closest face of the targetBlock
            Vec3d blockCenter = new Vec3d(targetBlock.getX() + 0.5, targetBlock.getY() + 0.5, targetBlock.getZ() + 0.5);
            Vec3d directionToBlock = blockCenter.subtract(playerPos).normalize();

            Direction closestFace = Direction.getFacing(directionToBlock.x, directionToBlock.y, directionToBlock.z);

            double x = targetBlock.getX() + 0.5;
            double y = targetBlock.getY() + 0.5;
            double z = targetBlock.getZ() + 0.5;

            switch (closestFace) {
                case DOWN: y = targetBlock.getY(); break;
                case UP: y = targetBlock.getY() + 1.0; break;
                case NORTH: z = targetBlock.getZ(); break;
                case SOUTH: z = targetBlock.getZ() + 1.0; break;
                case WEST: x = targetBlock.getX(); break;
                case EAST: x = targetBlock.getX() + 1.0; break;
            }
            targetAimPos = new Vec3d(x, y, z);
        }

        float[] rotations;
        if (exponentialSmoothing.get()) {
            rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                mc.player.getYaw(),
                mc.player.getPitch(),
                targetAimPos,
                playerPos,
                aimSpeed.get(),
                (float) event.frameTime,
                smoothingFactor.get()
            );
        } else {
            rotations = SmoothAimingUtils.calculateSmoothRotations(
                mc.player.getYaw(),
                mc.player.getPitch(),
                targetAimPos,
                playerPos,
                aimSpeed.get(),
                (float) event.frameTime
            );
        }

        mc.player.setYaw(rotations[0]);
        mc.player.setPitch(rotations[1]);
    }



    private BlockPos getPlacementPos(PlayerEntity target) {
        Vec3d targetPos = target.getPos();
        BlockPos playerFeetPos = new BlockPos((int) Math.floor(targetPos.x), 
                                            (int) Math.floor(targetPos.y), 
                                            (int) Math.floor(targetPos.z));
        BlockPos playerHeadPos = playerFeetPos.up();

        // Priority order: head first, then other positions
        BlockPos[] positions;

        if (predictMovement.get()) {
            Vec3d velocity = target.getVelocity();
            if (velocity.lengthSquared() > 0.01) {
                Vec3d predictedPos = targetPos;
                // Simple prediction: add velocity over a few ticks
                for (int i = 0; i < 5; i++) { // Predict 5 ticks ahead
                    predictedPos = predictedPos.add(velocity);
                    // Simulate gravity for falling targets
                    if (!target.isOnGround()) {
                        velocity = velocity.add(0, -0.08, 0); // Approximate gravity
                    }
                }

                BlockPos predictedFeet = new BlockPos((int) Math.floor(predictedPos.x),
                                                    (int) Math.floor(predictedPos.y),
                                                    (int) Math.floor(predictedPos.z));
                BlockPos predictedHead = predictedFeet.up();

                positions = new BlockPos[] {
                    predictedHead,      // Predicted head (highest priority)
                    playerHeadPos,      // Current head
                    predictedFeet,      // Predicted feet
                    playerFeetPos,      // Current feet
                    playerFeetPos.add(1, 0, 0),  // East
                    playerFeetPos.add(-1, 0, 0), // West
                    playerFeetPos.add(0, 0, 1),  // South
                    playerFeetPos.add(0, 0, -1)  // North
                };
            } else {
                positions = new BlockPos[] {
                    playerHeadPos,      // Head (highest priority)
                    playerFeetPos,      // Feet
                    playerFeetPos.add(1, 0, 0),  // East
                    playerFeetPos.add(-1, 0, 0), // West
                    playerFeetPos.add(0, 0, 1),  // South
                    playerFeetPos.add(0, 0, -1)  // North
                };
            }
        } else {
            positions = new BlockPos[] {
                playerHeadPos,      // Head (highest priority)
                playerFeetPos,      // Feet
                playerFeetPos.add(1, 0, 0),  // East
                playerFeetPos.add(-1, 0, 0), // West
                playerFeetPos.add(0, 0, 1),  // South
                playerFeetPos.add(0, 0, -1)  // North
            };
        }

        for (BlockPos pos : positions) {
            if (canPlaceAt(pos) && hasReachableAdjacentBlock(pos)) {
                return pos;
            }
        }

        return null;
    }

    private boolean canPlaceAt(BlockPos pos) {
        // Check if position is within range
        if (!mc.player.getBlockPos().isWithinDistance(pos, range.get())) {
            return false;
        }
        
        // Check if block is air and can be replaced
        BlockState state = mc.world.getBlockState(pos);
        return state.isAir() || 
               state.getBlock() == Blocks.WATER ||
               state.getBlock() == Blocks.LAVA;
    }

    private boolean hasReachableAdjacentBlock(BlockPos pos) {
        // Check if there's at least one solid adjacent block that we can place against
        Direction[] directions = {Direction.NORTH, Direction.SOUTH, Direction.EAST, 
                                Direction.WEST, Direction.UP, Direction.DOWN};
        
        for (Direction dir : directions) {
            BlockPos adjacent = pos.offset(dir);
            BlockState state = mc.world.getBlockState(adjacent);
            
            // Check if the adjacent block is solid and we can reach it
            if (!state.isAir() && 
                state.getBlock() != Blocks.WATER && 
                state.getBlock() != Blocks.LAVA &&
                mc.player.getEyePos().isInRange(Vec3d.ofCenter(adjacent), range.get())) {
                return true;
            }
        }
        
        return false;
    }

    @Override
    public void onDeactivate() {
        timer = 0;
        targetBlock = null;
        isAiming = false;
        lastFrameTime = System.nanoTime();
        lastCombatTime = 0;
        webbedPlayers.clear();
    }

    // Add a method to check if another module has taken action this tick
    private boolean hasAnotherModuleTakenAction() {
        // Check if MaceAura has taken action
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            return actionTakenField.getBoolean(null);
        } catch (Exception e) {
            // If we can't check, assume no conflict
            return false;
        }
    }

    // Mark that this module is taking action this tick
    private void markActionTaken() {
        try {
            Class<?> maceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
            java.lang.reflect.Field actionTakenField = maceAuraClass.getDeclaredField("actionTakenThisTick");
            actionTakenField.setAccessible(true);
            actionTakenField.setBoolean(null, true);
        } catch (Exception e) {
            // Ignore if we can't set the flag
        }
    }
}